services:
  - type: web
    name: invoyisi-frontend
    env: static
    buildCommand: npm install && npm run build
    staticPublishPath: ./dist
    routes:
      - type: rewrite
        source: /*
        destination: /index.html
    envVars:
      - key: NODE_VERSION
        value: 18
      - key: VITE_SUPABASE_URL
        fromDatabase:
          name: invoyisi-db
          property: connectionString
      - key: VITE_SUPABASE_ANON_KEY
        sync: false
