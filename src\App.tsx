import React, { lazy, Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import MainLayout from './layouts/MainLayout';
import AuthLayout from './layouts/AuthLayout';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import ToastProvider from './contexts/ToastContext';
import ThemeProvider from './contexts/ThemeContext';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import SuspenseWrapper from './components/ui/SuspenseWrapper';

// Lazy load components to improve initial load time
const Dashboard = lazy(() => import('./pages/Dashboard'));
const InvoicesPage = lazy(() => import('./pages/InvoicesPage'));
const ClientsPage = lazy(() => import('./pages/ClientsPage'));
const PaymentsPage = lazy(() => import('./pages/PaymentsPage'));
const RecordPaymentPage = lazy(() => import('./pages/RecordPaymentPage'));
const ReportsPage = lazy(() => import('./pages/ReportsPage'));
// RecurringPage is deprecated, using RecurringInvoicesPage instead
const RecurringInvoicesPage = lazy(() => import('./pages/RecurringInvoicesPage'));
const DocumentProcessingPage = lazy(() => import('./pages/DocumentProcessingPage'));
const SettingsPage = lazy(() => import('./pages/SettingsPage'));
const ProfilePage = lazy(() => import('./pages/ProfilePage'));
const AboutPage = lazy(() => import('./pages/AboutPage'));
const DiagnosticsPage = lazy(() => import('./pages/DiagnosticsPage'));
const SignInPage = lazy(() => import('./pages/AuthPages/SignInPage'));
const SignUpPage = lazy(() => import('./pages/AuthPages/SignUpPage'));
const ForgotPasswordPage = lazy(() => import('./pages/AuthPages/ForgotPasswordPage'));

// App content component that uses the auth context
const AppContent: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-900 mb-4"></div>
        <p className="text-gray-600">Loading your account...</p>
      </div>
    );
  }

  return (
    <Routes>
      {/* Auth Routes */}
      <Route element={<AuthLayout />}>
        <Route path="/signin" element={
          <Suspense fallback={<div>Loading...</div>}>
            <SignInPage />
          </Suspense>
        } />
        <Route path="/signup" element={
          <Suspense fallback={<div>Loading...</div>}>
            <SignUpPage />
          </Suspense>
        } />
        <Route path="/forgot-password" element={
          <Suspense fallback={<div>Loading...</div>}>
            <ForgotPasswordPage />
          </Suspense>
        } />
      </Route>

      {/* Public Routes */}
      <Route path="/about" element={
        <Suspense fallback={<div>Loading...</div>}>
          <AboutPage />
        </Suspense>
      } />
      <Route path="/diagnostics" element={
        <Suspense fallback={<div>Loading...</div>}>
          <DiagnosticsPage />
        </Suspense>
      } />

      {/* Protected Routes */}
      <Route path="/" element={
        isAuthenticated ? <MainLayout /> : <Navigate to="/signin" replace />
      }>
        {/* Redirect root to dashboard */}
        <Route index element={<Navigate to="/dashboard" replace />} />

        {/* Dashboard as a secondary route */}
        <Route path="dashboard" element={
          <Suspense fallback={<div>Loading dashboard...</div>}>
            <Dashboard />
          </Suspense>
        } />
        <Route path="invoices" element={
          <Suspense fallback={<div>Loading...</div>}>
            <InvoicesPage />
          </Suspense>
        } />
        <Route path="invoices/new" element={
          <Suspense fallback={<div>Loading...</div>}>
            <CreateInvoicePage />
          </Suspense>
        } />
        <Route path="invoices/:id" element={
          <Suspense fallback={<div>Loading...</div>}>
            <InvoiceDetailPage />
          </Suspense>
        } />
        <Route path="invoices/:id/edit" element={
          <Suspense fallback={<div>Loading...</div>}>
            <EditInvoicePage />
          </Suspense>
        } />
        <Route path="clients" element={
          <Suspense fallback={<div>Loading...</div>}>
            <ClientsPage />
          </Suspense>
        } />
        <Route path="clients/new" element={
          <Suspense fallback={<div>Loading...</div>}>
            <CreateClientPage />
          </Suspense>
        } />
        <Route path="clients/:id" element={
          <Suspense fallback={<div>Loading...</div>}>
            <ClientDetailPage />
          </Suspense>
        } />
        <Route path="clients/:id/edit" element={
          <Suspense fallback={<div>Loading...</div>}>
            <EditClientPage />
          </Suspense>
        } />
        <Route path="payments" element={
          <Suspense fallback={<div>Loading...</div>}>
            <PaymentsPage />
          </Suspense>
        } />
        <Route path="payments/new" element={
          <Suspense fallback={<div>Loading...</div>}>
            <CreatePaymentPage />
          </Suspense>
        } />
        <Route path="reports" element={
          <Suspense fallback={<div>Loading...</div>}>
            <ReportsPage />
          </Suspense>
        } />
        <Route path="settings" element={
          <Suspense fallback={<div>Loading...</div>}>
            <SettingsPage />
          </Suspense>
        } />
        <Route path="profile" element={
          <Suspense fallback={<div>Loading...</div>}>
            <ProfilePage />
          </Suspense>
        } />
        <Route path="documents" element={
          <Suspense fallback={<div>Loading...</div>}>
            <DocumentProcessingPage />
          </Suspense>
        } />
      </Route>

      {/* Redirect any unknown routes to dashboard or signin */}
      <Route path="*" element={<Navigate to={isAuthenticated ? "/" : "/signin"} replace />} />
    </Routes>
  );
};

function App() {
  return (
    <Router>
      <ThemeProvider>
        <ToastProvider>
          <AuthProvider>
            <AppContent />
          </AuthProvider>
        </ToastProvider>
      </ThemeProvider>
    </Router>
  );
}

export default App;